/**
 * ============================================================================
 * 🚀 核心业务流程 - 应用主入口 (母子两层架构入口)
 * ============================================================================
 *
 * @fileoverview OTA订单处理系统主入口 - 母子两层架构入口
 * @description 应用启动器，负责初始化母子两层架构和核心业务流程
 *
 * @businessFlow 应用启动和架构初始化
 * 系统启动流程：
 * 1. 【当前文件职责】检查核心模块加载状态
 * 2. 【当前文件职责】初始化母子两层架构
 * 3. 【当前文件职责】启动业务流程控制器
 * 4. 【当前文件职责】建立向后兼容接口
 * 5. 准备接收用户输入，开始核心业务流程
 *
 * @architecture Application Entry Point (应用入口)
 * - 职责：系统启动和架构初始化
 * - 原则：简单启动，委托给专门的启动器
 * - 接口：全局应用实例和兼容性接口
 *
 * @dependencies 依赖关系
 * 上游依赖：无（应用入口）
 * 下游依赖：
 * - core/application-bootstrap.js (应用启动器)
 * - controllers/business-flow-controller.js (母层控制器)
 * - 所有子层实现文件
 *
 * @localProcessing 本地处理职责
 * - 🟢 检查模块加载状态
 * - 🟢 初始化应用架构
 * - 🟢 建立全局实例
 * - 🟢 设置向后兼容接口
 * - 🟢 错误处理和降级方案
 *
 * @remoteProcessing 远程处理职责
 * - ❌ 无（纯启动逻辑，不调用远程API）
 *
 * @compatibility 兼容性保证
 * - 保持现有window.app全局实例
 * - 保持现有的启动流程
 * - 提供向后兼容的服务访问方式
 *
 * @refactoringConstraints 重构约束
 * - ✅ 保持简单的启动逻辑
 * - ✅ 不能破坏现有的初始化流程
 * - ✅ 必须支持母子两层架构
 * - ✅ 保持向后兼容性
 *
 * @refactoringPlan 重构计划
 * 阶段1：保持现有启动逻辑，添加架构支持
 * 阶段2：集成母子两层架构初始化
 * 阶段3：优化启动性能和错误处理
 * 阶段4：完善向后兼容性
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-08-09
 * @lastModified 2025-08-09
 * @refactoringStatus 已支持母子两层架构
 */

/**
 * 初始化页面系统
 * 设置路由和页面管理器
 */
async function initializePageSystem() {
    try {
        console.log('🚀 初始化页面系统...');

        // 检查页面系统组件是否已加载
        if (!window.OTA?.router || !window.OTA?.pageManager) {
            throw new Error('页面系统组件未正确加载');
        }

        // 初始化页面管理器
        window.OTA.pageManager.init(window.OTA.router);

        // 设置多订单检测触发逻辑
        setupMultiOrderDetectionTrigger();

        console.log('✅ 页面系统初始化完成');

    } catch (error) {
        console.error('❌ 页面系统初始化失败:', error);
        // 页面系统初始化失败不应该阻止整个应用启动
        // 系统将继续使用原有的多订单浮窗模式
    }
}

/**
 * 多订单检测触发逻辑已迁移到备份文件夹
 * 原多订单功能已被移除
 */
function setupMultiOrderDetectionTrigger() {
    // 多订单功能已迁移，此函数保留以避免调用错误
    console.log('⚠️ 多订单功能已迁移到备份文件夹');
}

// 等待所有模块加载完成后启动应用
async function startApp() {
    console.log('🚀 开始启动OTA订单处理系统...');

    try {
        // 检查核心模块是否已加载
        if (!window.OTA || !window.OTA.container || !window.OTA.ApplicationBootstrap) {
            throw new Error('核心架构模块未正确加载，请检查script标签顺序');
        }

        // 创建启动协调器实例
        const bootstrap = new window.OTA.ApplicationBootstrap();

        // 启动应用
        const result = await bootstrap.start();

        if (result.success) {
            console.log(`✅ OTA系统启动成功，耗时: ${result.duration.toFixed(2)}ms`);

            // 暴露全局应用实例（用于调试和向后兼容）
            window.app = {
                bootstrap,
                container: window.OTA.container,
                serviceLocator: window.OTA.serviceLocator,
                getService: window.OTA.getService,
                version: '2.0.0-refactored',
                startTime: Date.now()
            };

            // 暴露到OTA命名空间
            window.OTA.app = window.app;

            // 初始化页面系统
            await initializePageSystem();

            // 🔧 修复：自动显示工作区（跳过登录）
            await autoShowWorkspace();

            // 设置页脚版本显示
            try {
                const el = document.getElementById('buildVersion');
                const manifest = window.OTA && window.OTA.scriptManifest;
                const version = manifest?.version || 'dev';
                const ts = manifest?.createdAt ? new Date(manifest.createdAt).toLocaleTimeString() : '';
                if (el) {
                    el.textContent = `🛠️ v${version}`;
                    if (ts) el.title = `构建时间: ${manifest.createdAt}`;
                }
            } catch(e) { /* 忽略版本显示错误 */ }

        } else {
            throw new Error(`系统启动失败: ${result.error}`);
        }

    } catch (error) {
        console.error('❌ 系统启动失败:', error);

        // 显示错误信息给用户
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
            background: #ff4444; color: white; padding: 20px; border-radius: 8px;
            font-family: Arial, sans-serif; z-index: 10000; max-width: 500px;
        `;
        errorDiv.innerHTML = `
            <h3>系统启动失败</h3>
            <p>${error.message}</p>
            <button onclick="location.reload()" style="background: white; color: #ff4444; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                重新加载
            </button>
        `;
        document.body.appendChild(errorDiv);
    }
}

// If loader is used, this file is loaded as last; start immediately.
// Otherwise, fallback to DOMContentLoaded to preserve legacy behavior.
if (document.readyState === 'complete' || document.readyState === 'interactive') {
    startApp();
} else {
    document.addEventListener('DOMContentLoaded', startApp);
}

// 🧹 已清理：移除了传统应用程序类（LegacyOTAApplication）
// 现在统一使用新的启动协调器（ApplicationBootstrap）进行初始化
// 这简化了代码结构，避免了重复的初始化逻辑
// 🧹 已清理：移除了传统应用程序类的方法
// 这些功能现在由ApplicationBootstrap统一处理
// 🧹 已清理：移除了传统应用程序类的模块初始化方法
// 现在由ApplicationBootstrap统一处理模块初始化
// 🧹 已清理：移除了传统应用程序类的事件监听和数据检查方法
// 这些功能现在由ApplicationBootstrap和相关管理器处理
// 🧹 已清理：移除了传统应用程序类的所有方法
// 包括token刷新、登录状态恢复、错误处理等功能
// 这些功能现在由ApplicationBootstrap和相关管理器统一处理
// 🧹 已清理：移除了传统应用程序类的所有剩余方法
// 包括数据检查、错误处理、系统信息显示、应用重启等功能
// 这些功能现在由ApplicationBootstrap和相关管理器统一处理

// 🧹 已清理：移除了复杂的系统健康检查函数
// 这个功能过于复杂且很少使用，现在由ApplicationBootstrap的简化版本处理

// 🧹 已清理：移除了复杂的监控控制台命令设置函数
// 这个功能过于复杂且很少使用，增加了系统复杂性
// 基本的调试功能现在由Logger直接提供

/**
 * 检查CSS是否完全加载
 * 确保DOM操作在CSS就绪后执行，避免样式冲突
 */
function checkCSSReady() {
    return new Promise((resolve) => {
        // 检查关键CSS变量是否可用
        const testElement = document.createElement('div');
        testElement.style.position = 'absolute';
        testElement.style.visibility = 'hidden';
        testElement.className = 'hidden'; // 测试utilities.css中的.hidden类
        document.body.appendChild(testElement);
        
        const checkStyles = () => {
            const computedStyle = window.getComputedStyle(testElement);
            const isHidden = computedStyle.display === 'none';
            
            if (isHidden && document.styleSheets.length > 0) {
                // CSS已加载完成
                document.body.removeChild(testElement);
                resolve();
            } else {
                // CSS可能还未完全加载，稍后再检查
                setTimeout(checkStyles, 50);
            }
        };
        
        // 如果DOM已准备好，立即检查；否则等待
        if (document.readyState === 'complete') {
            checkStyles();
        } else {
            window.addEventListener('load', checkStyles);
        }
    });
}

/**
 * 自动显示工作区（跳过登录）
 * 修复页面显示问题的永久解决方案
 */
async function autoShowWorkspace() {
    console.log('🔧 自动显示工作区...');

    try {
        // 【修复时序问题】等待CSS完全加载后再操作DOM
        await checkCSSReady();
        
        const loginPanel = document.getElementById('loginPanel');
        const workspace = document.getElementById('workspace');

        if (loginPanel && workspace) {
            // 【修复CSS状态冲突】使用CSS优先的状态控制，统一通过body.logged-in控制
            // 移除所有hidden类，避免与CSS状态控制冲突
            loginPanel.classList.remove('hidden');
            workspace.classList.remove('hidden');

            // 【单一状态源】只使用body.logged-in类控制所有UI状态
            document.body.classList.add('logged-in');
            
            // 设置辅助功能属性
            loginPanel.setAttribute('aria-hidden', 'true');
            workspace.setAttribute('aria-hidden', 'false');

            // 【强制重绘】确保CSS样式立即生效
            document.body.offsetHeight; // 触发重绘


            // 确保UI管理器知道当前状态
            if (window.OTA?.uiManager) {
                try {
                    window.OTA.uiManager.showWorkspace();
                } catch (error) {
                    console.warn('UIManager.showWorkspace调用失败:', error.message);
                }
            }


        } else {
            console.warn('⚠️ 登录面板或工作区元素不存在');
        }

    } catch (error) {
        console.error('❌ 自动显示工作区失败:', error);
    }
}